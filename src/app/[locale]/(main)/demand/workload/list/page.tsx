import { getWorkloads } from '@/action/workload-action';
import { Workload, WorkloadRequest } from '@/types/workload';
import { dateTime } from '@/utils';
import { comma } from '@/utils/index';
import { FaPlus } from 'react-icons/fa6';
import { WorkloadFooter } from './workload-footer';

import { getPointBase } from '@/action/point-action';
import { getUser } from '@/action/user-action';
import { getSession } from '@/auth';
import { UserResponse } from '@/types/user';
import { getLocale, getTranslations } from 'next-intl/server';
import Link from 'next/link';

type PageProps = {
  searchParams: { [key: string]: string | string[] | undefined };
};

/**
 * @brief Workload Page
 * @param searchParams
 * @returns
 */
export default async function WorkLoadListPage({ searchParams }: PageProps) {
  const locale = await getLocale();
  const t_i18n = await getTranslations('i18nData');

  const session = await getSession();

  const userResponse: UserResponse = await getUser(session.user.email);
  const user = userResponse.status === 200 ? userResponse.user : null;

  //환율정보 요청
  const pointBaseResponse = await getPointBase();
  const krw = pointBaseResponse.status == 200 ? pointBaseResponse.data.krw : 0;
  const pointBase = pointBaseResponse.data;

  // Workload 목록 요청
  const queryParam: WorkloadRequest = {
    owner: '',
    startNum: 0,
    scaleNum: 100,
    category: '',
    target: '',
    state: '',
    startTime: '',
    endTime: '',
    sortName: 'ser',
    sortType: 'DESC',
    isDedicated: user.isDedicated
  };

  const data = await getWorkloads(queryParam);

  //WorkLoad 목록 세팅

  const workloads: Workload[] = data.workloads == undefined ? [] : data.workloads;

  /**
   * @brief Target 이름 변경
   * @param item
   * @returns
   */
  const convertTargetName = (item: string) => {
    if (item === 'pc') {
      return 'PC';
    } else if (item === 'svr') {
      return 'SERVER';
    } else if (item === 'csp') {
      return 'CLOUD';
    } else {
      return 'ANY';
    }
  };

  /**
   * @brief Category 이름 변경
   * @param item
   * @returns
   */
  const convertCategory = (item: string) => {
    if (item == 'infer') {
      return t_i18n('workload_category_inference');
    } else if (item == 'learn') {
      return t_i18n('workload_category_learning');
    } else if (item == 'job') {
      return t_i18n('workload_category_batch');
    }
  };

  /**
   * @brief 상태변경
   * @param item
   * @returns
   */
  const convertState = (item: string) => {
    if (item == 'open') {
      return <span className="badge-gray badge badge-pill badge-sm text-nowrap !px-4 !text-[16px]">{t_i18n('workload_status_open')}</span>;
    } else if (item == 'deploy') {
      return (
        <span className="p badge badge-pill badge-sm badge-success text-nowrap !px-4 !text-[16px]">{t_i18n('workload_status_deploy')}</span>
      );
    } else if (item == 'finish') {
      return (
        <span className="badge badge-pill badge-sm badge-danger text-nowrap !px-4 !text-[16px]">{t_i18n('workload_status_finish')}</span>
      );
    }
  };
  /**
   * @brief 금액 변환
   * @param item
   * @returns
   */
  const convertPoint = (item: Workload) => {
    let point = Math.round(item.amount * pointBase.unitPoint);
    let unitPoint = Math.round(item.unitAmount * pointBase.unitPoint);
    let usagePoint = Math.round(item.usageAmount * pointBase.unitPoint);

    if (locale == 'en') {
      return (
        <>
          <div className="flex items-center gap-2.5">
            <span className="text-[16px] font-medium leading-[16px] text-gray-500 max-lg:w-48">{t_i18n('workload_label_price_total')}</span>
            <span className="text-[16px] font-semibold leading-[16px] text-primary"> {'P ' + comma(Math.round(point))} </span>
            {/*<span className="flex items-center text-[16px] font-semibold leading-[16px] text-gray-700">/ {'$ ' + amount} </span>*/}
          </div>
          <div className="flex items-center gap-2.5">
            <span className="text-[16px] font-medium leading-[16px] text-gray-500 max-lg:w-48">{t_i18n('workload_label_price_unit')}</span>
            <span className="text-[16px] font-semibold leading-[16px] text-primary">{'P ' + comma(Math.round(unitPoint))}</span>
            {/*<span className="flex items-center text-[16px] font-semibold leading-[16px] text-gray-700">/ {'$ ' + unitAmount} </span>*/}
          </div>
          <div className="flex items-center gap-2.5">
            <span className="text-[16px] font-medium leading-[16px] text-gray-500 max-lg:w-48">{t_i18n('workload_label_price_usage')}</span>
            <span className="text-[16px] font-semibold leading-[16px] text-primary">{'P ' + comma(Math.round(usagePoint))}</span>
            {/*<span className="flex items-center text-[16px] font-semibold leading-[16px] text-gray-700">/ {'$ ' + usageAmount} </span>*/}
          </div>
        </>
      );
    } else {
      return (
        <>
          <div className="flex items-center gap-2.5">
            <span className="w-28 text-[16px] font-medium leading-[16px] text-gray-500 lg:!w-auto">
              {t_i18n('workload_label_price_total')}
            </span>
            <span className="text-[16px] font-semibold leading-[16px] text-primary">{'P ' + comma(Math.round(point))}</span>
            {/*<span className="flex items-center text-[16px] font-semibold leading-[16px] text-gray-700">/ {'₩ ' + comma(amount)}</span>*/}
          </div>
          <div className="flex items-center gap-2.5">
            <span className="w-28 text-[16px] font-medium leading-[16px] text-gray-500 lg:!w-auto">
              {t_i18n('workload_label_price_unit')}
            </span>
            <span className="text-[16px] font-semibold leading-[16px] text-primary">{'P ' + comma(Math.round(unitPoint))}</span>
            {/*<span className="flex items-center text-[16px] font-semibold leading-[16px] text-gray-700">/ {'₩ ' + comma(unitAmount)} </span>*/}
          </div>
          <div className="flex items-center gap-2.5">
            <span className="w-28 text-[16px] font-medium leading-[16px] text-gray-500 lg:!w-auto">
              {t_i18n('workload_label_price_usage')}
            </span>
            <span className="text-[16px] font-semibold leading-[16px] text-primary">{'P ' + comma(Math.round(usagePoint))}</span>
            {/*<span className="flex items-center text-[16px] font-semibold leading-[16px] text-gray-700">/ {'₩ ' + comma(usageAmount)}</span>*/}
          </div>
        </>
      );
    }
  };

  return (
    <>
      <div className="container-fixed2"></div>
    </>
  );
}
