'use client';
import { Workload } from '@/types/workload';

type PageProps = {
  workload: Workload;
};

export default function WorkloadCard({ workload }: PageProps) {
  /**
   * @brief 상태변경
   * @param item
   * @returns
   */
  const convertState = (item: Workload) => {
    if (item != undefined && item != null) {
      if (item.state == 'open') {
        return <span className="badge badge-pill badge-lg badge-outline h-[32px] text-nowrap !text-[16px]">미배포</span>;
      } else if (item.state == 'deploy') {
        return <span className="badge badge-pill badge-lg badge-outline badge-success h-[32px] text-nowrap !text-[16px]">배포</span>;
      } else if (item.state == 'finish' || item.state === 'finsh') {
        return <span className="badge badge-pill badge-lg badge-outline badge-danger h-[32px] text-nowrap !text-[16px]">종료</span>;
      }
    } else {
      return '';
    }
  };
  return (
    <>
      <div className="card">
        <div className="card-header">{convertState(workload.state)}</div>
        <div className="card-body"></div>
      </div>
    </>
  );
}
